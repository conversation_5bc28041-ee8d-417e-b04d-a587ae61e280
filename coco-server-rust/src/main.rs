mod app_state;
mod auth;
mod config;
mod database;
mod error;
mod handlers;
mod health;
mod logging;
mod middleware;
mod models;
mod repositories;
mod services;
mod tls;

use std::{net::SocketAddr, sync::Arc};

use axum::{
    routing::{delete, get, post, put},
    Router,
};
use config::{app_config::AppConfig, config_manager::ConfigManager};
use handlers::{
    info_handler::info_handler, setup_handler::initialize_handler,
    websocket_handler::websocket_handler,
};
use middleware::auth_middleware::auth_middleware;
use tower_http::cors::CorsLayer;

#[tokio::main]
async fn main() -> anyhow::Result<()> {
    // 加载基础配置
    let config_manager =
        ConfigManager::new().map_err(|e| anyhow::anyhow!("Failed to load configuration: {}", e))?;

    // 创建应用配置
    let app_config = AppConfig::from_config(config_manager.get_config())
        .map_err(|e| anyhow::anyhow!("Failed to create app configuration: {}", e))?;

    // 初始化日志系统
    logging::init_logging(&app_config.logging)
        .map_err(|e| anyhow::anyhow!("Failed to initialize logging: {}", e))?;

    tracing::info!("Starting Coco Server with dual-port architecture...");
    tracing::info!("Environment: {}", app_config.app_info.environment);
    tracing::info!("Version: {}", app_config.app_info.version);

    // 验证应用配置
    app_config
        .validate()
        .map_err(|e| anyhow::anyhow!("Invalid application configuration: {}", e))?;

    tracing::info!("Configuration loaded and validated successfully");

    // 记录服务器配置信息
    tracing::info!("Server name: {}", app_config.server.name);
    tracing::info!("Server public access: {}", app_config.server.public);
    tracing::info!("HTTPS enabled: {}", app_config.server.enable_https);
    tracing::info!("Request timeout: {}s", app_config.server.request_timeout);
    tracing::info!("Max connections: {}", app_config.server.max_connections);

    // 将配置包装在Arc中以便在线程间共享
    let config_manager = Arc::new(config_manager);
    let app_config = Arc::new(app_config);

    // 获取Web服务和API服务的绑定地址
    let web_addr = parse_binding_address(&app_config.server.web_binding);
    let api_addr = parse_binding_address(&app_config.server.api_binding);

    tracing::info!("Web service will bind to address: {}", web_addr);
    tracing::info!("API service will bind to address: {}", api_addr);

    // 记录TLS配置信息
    let use_https = app_config.server.enable_https;
    if use_https {
        tracing::info!("HTTPS is enabled");
        let cert_path = std::env::var("CERT_PATH").unwrap_or_else(|_| "cert.pem".to_string());
        let key_path = std::env::var("KEY_PATH").unwrap_or_else(|_| "key.pem".to_string());
        tracing::debug!("Certificate path: {}, Key path: {}", cert_path, key_path);
    } else {
        tracing::info!("HTTPS is disabled, using HTTP");
    }

    // 启动Web服务和API服务
    let web_server_handle = tokio::spawn(start_web_server(
        config_manager.clone(),
        web_addr,
        use_https,
    ));
    let api_server_handle = tokio::spawn(start_api_server(
        config_manager.clone(),
        api_addr,
        use_https,
    ));

    // 等待任一服务完成（这通常意味着出现错误）
    // 使用futures::future::select来处理服务完成事件
    use futures::future::{select, Either};

    match select(web_server_handle, api_server_handle).await {
        Either::Left((web_result, api_server_handle)) => {
            match web_result {
                Ok(Ok(_)) => {
                    tracing::info!("Web server completed successfully");
                }
                Ok(Err(e)) => {
                    tracing::error!("Web server failed: {}", e);
                }
                Err(e) => {
                    tracing::error!("Web server task panicked: {}", e);
                }
            }

            // 继续等待API服务完成
            if let Ok(api_result) = api_server_handle.await {
                match api_result {
                    Ok(_) => tracing::info!("API server also completed successfully"),
                    Err(e) => tracing::error!("API server failed: {}", e),
                }
            } else {
                tracing::error!("API server task panicked");
            }
        }
        Either::Right((api_result, web_server_handle)) => {
            match api_result {
                Ok(Ok(_)) => {
                    tracing::info!("API server completed successfully");
                }
                Ok(Err(e)) => {
                    tracing::error!("API server failed: {}", e);
                }
                Err(e) => {
                    tracing::error!("API server task panicked: {}", e);
                }
            }

            // 继续等待Web服务完成
            if let Ok(web_result) = web_server_handle.await {
                match web_result {
                    Ok(_) => tracing::info!("Web server also completed successfully"),
                    Err(e) => tracing::error!("Web server failed: {}", e),
                }
            } else {
                tracing::error!("Web server task panicked");
            }
        }
    }

    tracing::info!("Server shutdown complete");

    Ok(())
}

/// 解析绑定地址字符串为SocketAddr
fn parse_binding_address(binding: &str) -> SocketAddr {
    // 绑定格式通常是 "0.0.0.0:9000" 或 ":9000"
    if let Some(colon_pos) = binding.rfind(':') {
        let ip_part = &binding[..colon_pos];
        let port_str = &binding[colon_pos + 1..];

        if let Ok(port) = port_str.parse::<u16>() {
            if !ip_part.is_empty() && ip_part != "0.0.0.0" {
                if let Ok(ip) = ip_part.parse::<std::net::IpAddr>() {
                    return SocketAddr::new(ip, port);
                }
            }
            return SocketAddr::from(([0, 0, 0, 0], port));
        }
    }

    // 默认回退到9000端口
    SocketAddr::from(([0, 0, 0, 0], 9000))
}

/// 启动Web服务
async fn start_web_server(
    config_manager: Arc<ConfigManager>,
    addr: SocketAddr,
    use_https: bool,
) -> anyhow::Result<()> {
    use handlers::{
        account_handler::profile_handler,
        connector_handler::{
            search_connector_get, search_connector_options, search_connector_post,
        },
        datasource_handler::{
            search_datasource_get, search_datasource_options, search_datasource_post,
        },
        info_handler::{health_handler, info_handler},
        mcp_server_handler::{
            search_mcp_server_get, search_mcp_server_options, search_mcp_server_post,
        },
        static_handler::static_file_handler,
    };

    // 创建CORS层，允许所有来源、方法和头部
    let cors_layer = CorsLayer::very_permissive();

    // 构建Web应用路由（用于提供Web界面）
    let api_routes = Router::new()
        .route("/health", axum::routing::get(health_handler))
        .route("/_info", axum::routing::get(info_handler))
        .route("/provider/_info", axum::routing::get(info_handler))
        .route("/account/profile", axum::routing::get(profile_handler))
        .route(
            "/datasource/_search",
            axum::routing::get(search_datasource_get)
                .post(search_datasource_post)
                .options(search_datasource_options),
        )
        .route(
            "/connector/_search",
            axum::routing::get(search_connector_get)
                .post(search_connector_post)
                .options(search_connector_options),
        )
        .route(
            "/mcp_server/_search",
            axum::routing::get(search_mcp_server_get)
                .post(search_mcp_server_post)
                .options(search_mcp_server_options),
        );

    // SSO路由需要在静态路由之前，避免被fallback捕获
    let sso_routes = Router::new().route(
        "/sso/login/cloud",
        axum::routing::get(handlers::sso_handler::sso_login_handler),
    );

    let static_routes = Router::new()
        .route("/", axum::routing::get(static_file_handler))
        .fallback(axum::routing::get(static_file_handler));

    // 为Web服务器创建轻量级AppState（不需要数据库连接）
    use crate::auth::token_blacklist::TokenBlacklist;
    let token_blacklist = Arc::new(TokenBlacklist::new());

    // 创建一个轻量级的AppState用于Web服务器
    // 注意：Web服务器不需要数据库连接，所以我们暂时保持使用ConfigManager
    let web_app = api_routes
        .merge(sso_routes) // SSO路由优先
        .merge(static_routes) // 静态路由最后，包含fallback
        .layer(cors_layer)
        .with_state(config_manager.clone());

    tracing::info!("Starting Web server on {}", addr);

    if use_https {
        tracing::warn!("HTTPS for Web server is not yet implemented, starting HTTP server instead");
    }

    // 运行HTTP服务器
    let listener = tokio::net::TcpListener::bind(&addr)
        .await
        .map_err(|e| anyhow::anyhow!("Failed to bind to address {}: {}", addr, e))?;

    let server_result = axum::serve(listener, web_app).await;

    match &server_result {
        Ok(_) => tracing::info!("Web HTTP server stopped gracefully"),
        Err(e) => tracing::error!("Web HTTP server failed: {}", e),
    }

    server_result.map_err(|e| anyhow::anyhow!("Failed to start Web HTTP server: {}", e))
}

/// 启动API服务
async fn start_api_server(
    config_manager: Arc<ConfigManager>,
    addr: SocketAddr,
    use_https: bool,
) -> anyhow::Result<()> {
    // 创建CORS层，允许所有来源、方法和头部
    let cors_layer = CorsLayer::very_permissive();

    // 构建API应用路由
    // 初始化全局数据库连接
    use crate::{
        auth::token_blacklist::TokenBlacklist,
        database::{init_database, schema::SchemaInitializer},
        handlers::{
            account_handler::{
                change_password_handler, login_handler, logout_handler, profile_handler,
            },
            chat_handler::chat_handler,
            datasource_handler::{
                search_datasource_get, search_datasource_options, search_datasource_post,
            },
            info_handler::health_handler,
            model_provider_handler::{
                create_model_provider_handler, delete_model_provider_handler,
                get_model_provider_handler, search_model_provider_get_handler,
                search_model_provider_options_handler, search_model_provider_post_handler,
                update_model_provider_handler,
            },
            models_handler::models_handler,
            token_handler::{
                delete_access_token_handler, get_token_stats_handler, list_access_tokens_handler,
                rename_access_token_handler, request_access_token_handler,
            },
        },
        repositories::token_repository::TokenRepository,
        services::token_service::TokenService,
    };

    tracing::info!("正在初始化全局数据库连接...");

    // ✅ 使用ConfigManager的数据库配置，而不是默认值
    let db_config = config_manager.get_database_config();
    tracing::info!("数据库配置获取成功");

    // 初始化全局数据库连接
    init_database(&db_config)
        .await
        .map_err(|e| anyhow::anyhow!("Failed to initialize database: {}", e))?;

    tracing::info!("全局数据库连接初始化成功");

    // 初始化数据库Schema（不需要迁移，直接定义）
    tracing::info!("正在初始化数据库Schema...");

    SchemaInitializer::initialize_all()
        .await
        .map_err(|e| anyhow::anyhow!("Failed to initialize database schema: {}", e))?;

    // 创建TokenRepository实例（使用全局DB连接）
    let token_repository = Arc::new(TokenRepository::new_with_global_db());

    // 创建TokenService实例
    let token_service = Arc::new(TokenService::new(token_repository.clone()));

    // 创建TokenBlacklist实例
    let token_blacklist = Arc::new(TokenBlacklist::new());

    // 创建JWT缓存实例
    use std::time::Duration;

    use crate::auth::jwt_cache::JwtCache;
    let jwt_cache = Arc::new(JwtCache::new(
        Duration::from_secs(300), // 5分钟TTL
        1000,                     // 最大1000个缓存项
    ));

    // 创建应用状态（不再需要db_client）
    use crate::app_state::AppState;
    let app_state = AppState::new_with_global_db(
        config_manager.clone(),
        token_repository.clone(),
        token_service.clone(),
        token_blacklist.clone(),
        jwt_cache.clone(),
    );

    // 创建不需要认证的路由（使用AppState）
    let public_routes = Router::new()
        .route("/_info", get(info_handler))
        .route("/health", get(health_handler)) // 健康检查端点
        .route("/account/login", post(login_handler)) // 登录端点
        .route("/setup/_initialize", post(initialize_handler))
        .with_state(config_manager.clone());

    // 创建需要认证的路由（使用ConfigManager）
    let protected_routes = Router::new()
        .route("/account/profile", get(profile_handler)) // 用户配置文件端点
        .route(
            "/account/password",
            axum::routing::put(change_password_handler),
        ) // 密码修改端点
        .route("/api/v1/models", get(models_handler))
        .route("/api/v1/chat", post(chat_handler))
        .route("/ws", axum::routing::get(websocket_handler))
        // DataSource API 路由
        .route("/datasource/_search", get(search_datasource_get))
        .route("/datasource/_search", post(search_datasource_post))
        .route(
            "/datasource/_search",
            axum::routing::options(search_datasource_options),
        )
        // Model Provider API 路由
        .route("/model_provider/", post(create_model_provider_handler))
        .route("/model_provider/{id}", get(get_model_provider_handler))
        .route("/model_provider/{id}", put(update_model_provider_handler))
        .route(
            "/model_provider/{id}",
            delete(delete_model_provider_handler),
        )
        // Model Provider 搜索 API 路由
        .route(
            "/model_provider/_search",
            get(search_model_provider_get_handler),
        )
        .route(
            "/model_provider/_search",
            post(search_model_provider_post_handler),
        )
        .route(
            "/model_provider/_search",
            axum::routing::options(search_model_provider_options_handler),
        )
        .route_layer(axum::middleware::from_fn_with_state(
            app_state.clone(),
            auth_middleware,
        ))
        .with_state(config_manager.clone());

    // 创建API令牌管理路由（需要认证）
    let token_routes = Router::new()
        // 注销端点
        .route("/account/logout", post(logout_handler))
        // API令牌管理端点
        .route(
            "/auth/request_access_token",
            post(request_access_token_handler),
        )
        .route("/auth/access_token/_cat", get(list_access_tokens_handler))
        .route("/auth/access_token/_stats", get(get_token_stats_handler))
        .route(
            "/auth/access_token/{token_id}",
            delete(delete_access_token_handler),
        )
        .route(
            "/auth/access_token/{token_id}/_rename",
            post(rename_access_token_handler),
        )
        .route_layer(axum::middleware::from_fn_with_state(
            app_state.clone(),
            auth_middleware,
        ))
        .with_state(app_state.clone());

    // 合并所有路由器
    let app = public_routes
        .merge(protected_routes)
        .merge(token_routes)
        .layer(cors_layer);

    tracing::info!("Starting API server on {}", addr);

    if use_https {
        tracing::warn!("HTTPS for API server is not yet implemented, starting HTTP server instead");
    }

    // 运行HTTP服务器
    let listener = tokio::net::TcpListener::bind(&addr)
        .await
        .map_err(|e| anyhow::anyhow!("Failed to bind to address {}: {}", addr, e))?;

    let server_result = axum::serve(listener, app).await;

    match &server_result {
        Ok(_) => tracing::info!("API HTTP server stopped gracefully"),
        Err(e) => tracing::error!("API HTTP server failed: {}", e),
    }

    server_result.map_err(|e| anyhow::anyhow!("Failed to start API HTTP server: {}", e))
}

#[cfg(test)]
mod tests {
    use axum::{
        body::Body,
        http::{Request, StatusCode},
        routing::get,
    };
    use tower::ServiceExt;
    use tower_http::cors::CorsLayer;

    use super::*;

    #[tokio::test]
    async fn test_cors_headers_present() {
        // 创建一个简单的测试路由，不需要配置管理器
        let app = Router::new()
            .route("/_info", get(|| async { "Info endpoint" }))
            .layer(CorsLayer::very_permissive());

        // 创建一个带有Origin头部的请求（模拟跨域请求）
        let request = Request::builder()
            .uri("/_info")
            .header("origin", "http://localhost:3000")
            .body(Body::empty())
            .unwrap();

        // 发送请求
        let response = app.oneshot(request).await.unwrap();

        // 验证响应状态码
        assert_eq!(response.status(), StatusCode::OK);

        // 验证CORS头部是否存在
        assert!(response
            .headers()
            .contains_key("access-control-allow-origin"));
    }
}
